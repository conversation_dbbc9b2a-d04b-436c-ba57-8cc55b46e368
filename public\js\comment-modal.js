// Comment Modal Functions

// Open comment modal
function openCommentModal(postId) {
    const modal = document.getElementById(`commentModal-${postId}`);
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Sync like states and counts between modal and main feed
    syncLikeStates(postId);
    syncCounts(postId);

    // Initialize sort dropdown event listener
    initializeSortDropdown(postId);

    // Focus on the comment input for better UX
    setTimeout(() => {
        const commentForm = modal.querySelector('.comment-form');
        const textarea = commentForm?.querySelector('textarea[name="content"]');
        if (textarea) {
            textarea.focus();
        }

        // Initialize reaction system for modal content
        if (window.facebookReactionSystem) {
            window.facebookReactionSystem.createReactionPopups();
        }

        // Sync summary data and reaction state when modal opens
        if (window.postSummaryUpdater) {
            window.postSummaryUpdater.updatePostSummary(postId);
        }

        // Sync reaction button state with main feed
        syncModalReactionWithMainFeed(postId);
    }, 100);
}



// Sync like states between modal and main feed
function syncLikeStates(postId) {
    const mainLikeBtn = document.getElementById(`like-btn-${postId}`);
    const modalLikeBtn = document.getElementById(`like-btn-${postId}-modal`);

    if (mainLikeBtn && modalLikeBtn) {
        const mainHeartIcon = mainLikeBtn.querySelector('svg');
        const modalHeartIcon = modalLikeBtn.querySelector('svg');

        // Check if main feed shows liked state
        const isLikedInMain = mainHeartIcon.classList.contains('text-red-600') || mainHeartIcon.classList.contains('fill-current');

        // Sync modal to match main feed state
        if (isLikedInMain) {
            modalHeartIcon.classList.remove('text-gray-400');
            modalHeartIcon.classList.add('text-red-500', 'fill-current');
            modalHeartIcon.setAttribute('fill', 'currentColor');
        } else {
            modalHeartIcon.classList.remove('text-red-500', 'fill-current');
            modalHeartIcon.classList.add('text-gray-400');
            modalHeartIcon.setAttribute('fill', 'none');
        }
    }
}

// Sync counts between modal and main feed
function syncCounts(postId) {
    // Sync like counts
    const mainLikeCount = document.getElementById(`like-count-${postId}`);
    const modalLikeCount = document.getElementById(`modal-likes-count-${postId}`);
    const modalLikeSection = document.getElementById(`modal-likes-section-${postId}`);

    if (mainLikeCount && modalLikeCount) {
        // Extract number from main feed format "X likes"
        const mainCountText = mainLikeCount.textContent.trim();
        const likesMatch = mainCountText.match(/(\d+)\s+likes?/);
        const likesCount = likesMatch ? parseInt(likesMatch[1]) : 0;

        // Update modal like count
        modalLikeCount.textContent = likesCount;

        // Show/hide like section based on count
        if (modalLikeSection) {
            if (likesCount > 0) {
                modalLikeSection.style.display = 'flex';
            } else {
                modalLikeSection.style.display = 'none';
            }
        }
    }

    // Sync comment counts
    const mainCommentCount = document.getElementById(`comments-count-${postId}`);
    const modalCommentCount = document.getElementById(`modal-comments-count-${postId}`);

    if (mainCommentCount && modalCommentCount) {
        // Extract number from main feed format "X comments"
        const mainCountText = mainCommentCount.textContent.trim();
        const commentsMatch = mainCountText.match(/(\d+)\s+comments?/);
        const commentsCount = commentsMatch ? parseInt(commentsMatch[1]) : 0;

        // Update modal comment count
        modalCommentCount.textContent = `${commentsCount} comments`;
    }
}

// Close comment modal
function closeCommentModal(postId, event = null) {
    if (event && event.target !== event.currentTarget) {
        return;
    }
    document.getElementById(`commentModal-${postId}`).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Open share comment modal
function openShareCommentModal(shareId) {
    const modal = document.getElementById(`shareCommentModal-${shareId}`);
    if (!modal) return;

    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Focus on the comment input for better UX
    setTimeout(() => {
        const commentForm = modal.querySelector('.share-comment-form');
        const textarea = commentForm?.querySelector('textarea[name="content"]');
        if (textarea) {
            textarea.focus();
        }

        // Initialize reaction system for modal content
        if (window.facebookReactionSystem) {
            window.facebookReactionSystem.createReactionPopups();
        }

        // Sync summary data and reaction state when modal opens
        if (window.postSummaryUpdater) {
            window.postSummaryUpdater.updateShareSummary(shareId);
        }

        // Sync reaction button state with main feed
        syncShareModalReactionWithMainFeed(shareId);
    }, 100);
}

// Close share comment modal
function closeShareCommentModal(shareId, event = null) {
    if (event && event.target !== event.currentTarget) {
        return;
    }
    const modal = document.getElementById(`shareCommentModal-${shareId}`);
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
}

// Add share comment to modal DOM
function addShareCommentToModalDOM(shareId, comment) {
    const modal = document.getElementById(`shareCommentModal-${shareId}`);
    const isModalOpen = modal && !modal.classList.contains('hidden');

    if (isModalOpen) {
        // If modal is open, add to modal
        const modalCommentsList = modal.querySelector(`#modal-share-comments-list-${shareId}`);
        if (modalCommentsList) {
            // Remove "no comments" message if it exists
            const noComments = modalCommentsList.querySelector('.no-comments');
            if (noComments) {
                noComments.remove();
            }

            // Use modal-specific styling
            const commentHTML = createModalShareCommentHTML(comment, shareId);
            modalCommentsList.insertAdjacentHTML('afterbegin', commentHTML);

            // Initialize reactions for the new comment
            if (window.facebookReactionSystem) {
                setTimeout(() => {
                    window.facebookReactionSystem.createReactionPopups();
                }, 100);
            }
        }
    }

    // Update comment count
    updateShareCommentCount(shareId);
}

// Create modal-specific share comment HTML
function createModalShareCommentHTML(comment, shareId) {
    const timeAgo = formatTimeAgo(comment.created_at);
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;

    const canEdit = comment.user_id === getCurrentUserId() || isCurrentUserAdmin();
    const editedText = comment.created_at !== comment.updated_at ? '<span class="text-xs text-gray-400 italic">• edited</span>' : '';

    return `
        <div class="comment-item py-4 px-4 hover:bg-gray-50 transition-colors duration-150" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="h-10 w-10 rounded-full ring-1 ring-gray-300 shadow-sm"
                         src="${userAvatar}"
                         alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-100 rounded-xl p-4 shadow-sm border border-gray-200">
                        <div class="flex items-center space-x-2 mb-2">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                            <span class="text-xs text-gray-500">${timeAgo}</span>
                            ${canEdit ? `
                                <div class="relative ml-auto">
                                    <button onclick="toggleShareCommentDropdown(${comment.id})" class="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-200">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                        </svg>
                                    </button>
                                    <div id="share-comment-dropdown-${comment.id}" class="hidden absolute right-0 top-8 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                                        <button onclick="editShareComment(${comment.id}); hideShareCommentDropdown(${comment.id})"
                                                class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                            <span>Edit</span>
                                        </button>
                                        <button onclick="deleteShareComment(${comment.id}); hideShareCommentDropdown(${comment.id})"
                                                class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                            <span>Delete</span>
                                        </button>
                                    </div>
                                </div>
                            ` : ''}
                        </div>

                        <div class="comment-content" id="comment-content-${comment.id}">
                            <p class="text-gray-800 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>

                        <!-- Edit form (hidden by default) -->
                        <div class="comment-edit-form hidden mt-3" id="edit-comment-form-${comment.id}">
                            <form class="share-comment-edit-form" data-comment-id="${comment.id}">
                                <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                                <input type="hidden" name="_method" value="PUT">
                                <textarea name="content" rows="2"
                                          class="w-full bg-gray-50 text-gray-900 border border-gray-300 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">${comment.content}</textarea>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="cancelEditShareComment(${comment.id})"
                                            class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                    <button type="submit"
                                            class="px-3 py-1.5 text-sm bg-custom-green text-white rounded-md hover:bg-green-600 transition-colors">Save</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Comment Actions -->
                    <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                        <!-- Facebook-style Reactions -->
                        <div class="facebook-reactions" data-target-type="comment" data-target-id="${comment.id}">
                            <button class="reaction-btn flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors group"
                                    data-reaction-type="like">
                                <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v10M7 20H2v-10h5v10z" />
                                </svg>
                                <span class="font-medium">Like</span>
                            </button>
                        </div>

                        <button onclick="showShareReplyForm(${comment.id})" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors group">
                            <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                            </svg>
                            <span class="font-medium">Reply</span>
                        </button>

                        ${editedText}
                    </div>

                    <!-- Reply Form (hidden by default) -->
                    <div class="reply-form hidden mt-3 ml-4" id="share-reply-form-${comment.id}">
                        <form class="share-comment-reply-form" data-parent-id="${comment.id}" data-share-id="${shareId}">
                            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                            <div class="flex space-x-3">
                                <div class="flex-shrink-0">
                                    <img class="h-8 w-8 rounded-full ring-1 ring-gray-300 shadow-sm"
                                         src="${getCurrentUserAvatar()}"
                                         alt="Your avatar">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="relative">
                                        <textarea name="content" rows="1"
                                                  placeholder="Write a reply..."
                                                  class="w-full px-3 py-2 bg-gray-50 text-gray-900 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm hover:bg-gray-100 transition-colors duration-200"
                                                  required></textarea>
                                    </div>
                                    <div class="mt-2 flex justify-end space-x-2">
                                        <button type="button" onclick="hideShareReplyForm(${comment.id})"
                                                class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                        <button type="submit"
                                                class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                            Reply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Replies Section -->
                    ${comment.replies && comment.replies.length > 0 ? `
                        <div class="mt-3">
                            <!-- View Replies Button -->
                            <button onclick="toggleModalShareReplies(${comment.id})"
                                    class="flex items-center space-x-2 text-gray-600 hover:text-gray-800 text-sm font-medium transition-colors ml-2"
                                    id="modal-share-view-replies-btn-${comment.id}">
                                <svg class="w-4 h-4 transform transition-transform" id="modal-share-replies-arrow-${comment.id}" viewBox="0 0 24 24">
                                    <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                                </svg>
                                <span id="modal-share-replies-text-${comment.id}">
                                    View ${comment.replies.length} ${comment.replies.length === 1 ? 'reply' : 'replies'}
                                </span>
                            </button>

                            <!-- Replies List (hidden by default) -->
                            <div class="hidden mt-3 ml-4 space-y-3 border-l-2 border-gray-600 pl-4" id="modal-share-replies-list-${comment.id}">
                                ${comment.replies.map(reply => createModalShareCommentHTML(reply, shareId)).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

// Helper functions for modal comments
function getCurrentUserId() {
    // Get current user ID from meta tag or global variable
    const userMeta = document.querySelector('meta[name="user-id"]');
    return userMeta ? parseInt(userMeta.getAttribute('content')) : null;
}

function isCurrentUserAdmin() {
    // Check if current user is admin from meta tag or global variable
    const adminMeta = document.querySelector('meta[name="user-is-admin"]');
    return adminMeta ? adminMeta.getAttribute('content') === 'true' : false;
}

function getCurrentUserAvatar() {
    // Get current user avatar from meta tag or generate default
    const avatarMeta = document.querySelector('meta[name="user-avatar"]');
    const nameMeta = document.querySelector('meta[name="user-name"]');

    if (avatarMeta && avatarMeta.getAttribute('content')) {
        return `/storage/${avatarMeta.getAttribute('content')}`;
    } else if (nameMeta) {
        return `https://ui-avatars.com/api/?name=${encodeURIComponent(nameMeta.getAttribute('content'))}&color=7BC74D&background=EEEEEE`;
    }
    return 'https://ui-avatars.com/api/?name=User&color=7BC74D&background=EEEEEE';
}

function formatTimeAgo(dateString) {
    // Simple time ago formatting - you can enhance this
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
}

// Create modal share comment HTML
function createModalShareCommentHTML(comment, shareId) {
    const timeAgo = formatTimeAgo(comment.created_at);
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;

    return `
        <div class="comment-item py-4 px-4 hover:bg-gray-50 transition-colors duration-150" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="h-10 w-10 rounded-full ring-1 ring-gray-300 shadow-sm"
                         src="${userAvatar}"
                         alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-100 rounded-xl p-4 shadow-sm border border-gray-200">
                        <div class="flex items-center space-x-2 mb-2">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                            <span class="text-xs text-gray-500">${timeAgo}</span>
                        </div>
                        <div class="comment-content" id="comment-content-${comment.id}">
                            <p class="text-gray-800 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>
                    </div>

                    <!-- Comment Actions -->
                    <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                        <div class="reaction-wrapper relative inline-block">
                            <button class="reaction-btn flex items-center space-x-2 text-gray-500 transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
                                    data-target-id="${comment.id}"
                                    data-target-type="comment"
                                    data-current-reaction="">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
                                </svg>
                                <span class="text-sm font-medium">Like</span>
                            </button>
                        </div>

                        <button onclick="showShareReplyForm(${comment.id})" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors group">
                            <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                            </svg>
                            <span class="font-medium">Reply</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Update share comment count
function updateShareCommentCount(shareId) {
    // This is a simplified version - in a real app you'd fetch the actual count
    const countElement = document.getElementById(`modal-share-comments-count-${shareId}`);
    if (countElement) {
        const currentCount = parseInt(countElement.textContent.match(/\d+/)[0]);
        countElement.textContent = `${currentCount + 1} comments`;
    }

    // Also update the main feed count
    const mainCountElement = document.getElementById(`share-comments-count-${shareId}`);
    if (mainCountElement) {
        const currentCount = parseInt(mainCountElement.textContent.match(/\d+/)[0]);
        mainCountElement.textContent = `${currentCount + 1} comments`;
    }
}

// Toggle replies visibility in modal
function toggleModalReplies(commentId) {
    const repliesList = document.getElementById(`modal-replies-list-${commentId}`);
    const arrow = document.getElementById(`modal-replies-arrow-${commentId}`);
    const text = document.getElementById(`modal-replies-text-${commentId}`);
    
    if (repliesList && repliesList.classList.contains('hidden')) {
        repliesList.classList.remove('hidden');
        if (arrow) arrow.style.transform = 'rotate(180deg)';
        if (text) text.textContent = 'Hide replies';
    } else if (repliesList) {
        repliesList.classList.add('hidden');
        if (arrow) arrow.style.transform = 'rotate(0deg)';
        const replyCount = repliesList.children.length;
        if (text) text.textContent = `View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
    }
}

// Toggle share replies visibility in modal
function toggleModalShareReplies(commentId) {
    const repliesList = document.getElementById(`modal-share-replies-list-${commentId}`);
    const arrow = document.getElementById(`modal-share-replies-arrow-${commentId}`);
    const text = document.getElementById(`modal-share-replies-text-${commentId}`);
    
    if (repliesList && repliesList.classList.contains('hidden')) {
        repliesList.classList.remove('hidden');
        if (arrow) arrow.style.transform = 'rotate(180deg)';
        if (text) text.textContent = 'Hide replies';
    } else if (repliesList) {
        repliesList.classList.add('hidden');
        if (arrow) arrow.style.transform = 'rotate(0deg)';
        const replyCount = repliesList.children.length;
        if (text) text.textContent = `View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
    }
}

// Auto-resize textarea
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
}

// Override addCommentToDOM to work with modals
const originalAddCommentToDOM = window.addCommentToDOM;
window.addCommentToDOM = function(postId, comment) {
    // Check if modal is open first
    const modal = document.getElementById(`commentModal-${postId}`);
    const isModalOpen = modal && !modal.classList.contains('hidden');

    if (isModalOpen) {
        // If modal is open, only add to modal (don't add to main feed)
        const modalCommentsList = modal.querySelector(`#comments-list-${postId}`);
        if (modalCommentsList) {
            // Remove "no comments" message if it exists
            const noComments = modalCommentsList.querySelector('.no-comments');
            if (noComments) {
                noComments.remove();
            }

            // Use modal-specific styling
            const commentHTML = createModalCommentHTML(comment, postId);
            modalCommentsList.insertAdjacentHTML('afterbegin', commentHTML);

            // Initialize reactions for the new comment
            if (window.facebookReactionSystem) {
                setTimeout(() => {
                    window.facebookReactionSystem.createReactionPopups();
                }, 100);
            }
        }
    } else {
        // If modal is not open, add to main feed only
        if (originalAddCommentToDOM) {
            originalAddCommentToDOM(postId, comment);
        }
    }
};

// Function to add reply only to modal
function addModalReply(parentId, reply) {
    // Find the modal replies section (should be unique by ID)
    let modalReplyList = document.getElementById(`modal-replies-list-${parentId}`);

    if (!modalReplyList) {
        // Find the parent comment in the modal to create replies section
        const modalParentComment = document.querySelector(`[id^="commentModal-"] [data-comment-id="${parentId}"]`);

        if (modalParentComment) {
            // Create replies section if it doesn't exist
            const repliesSection = document.createElement('div');
            repliesSection.className = 'mt-3';
            repliesSection.innerHTML = `
                <button onclick="toggleModalReplies(${parentId})"
                        class="flex items-center space-x-2 text-gray-600 hover:text-gray-800 text-sm font-medium transition-colors ml-2"
                        id="modal-view-replies-btn-${parentId}">
                    <svg class="w-4 h-4 transform transition-transform" id="modal-replies-arrow-${parentId}" viewBox="0 0 24 24">
                        <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                    </svg>
                    <span id="modal-replies-text-${parentId}">
                        View 1 reply
                    </span>
                </button>
                <div class="hidden mt-3 ml-4 space-y-3 border-l-2 border-gray-600 pl-4" id="modal-replies-list-${parentId}">
                </div>
            `;

            modalParentComment.querySelector('.flex-1').appendChild(repliesSection);
            modalReplyList = document.getElementById(`modal-replies-list-${parentId}`);
        }
    }

    if (modalReplyList) {
        // Add the reply using the modal comment structure
        const replyHTML = createModalCommentHTML(reply, reply.commentable_id || reply.post_id);
        modalReplyList.insertAdjacentHTML('beforeend', replyHTML);

        // Initialize reactions for the new reply
        if (window.facebookReactionSystem) {
            window.facebookReactionSystem.createReactionPopups();
        }

        // Update the replies count
        const repliesText = document.getElementById(`modal-replies-text-${parentId}`);
        if (repliesText) {
            const currentCount = modalReplyList.children.length;
            repliesText.textContent = `View ${currentCount} ${currentCount === 1 ? 'reply' : 'replies'}`;
        }

        // Show replies if hidden
        if (modalReplyList.classList.contains('hidden')) {
            toggleModalReplies(parentId);
        }
    }
}

// Override the original addReplyToDOM to prevent double replies
const originalAddReplyToDOM = window.addReplyToDOM;
window.addReplyToDOM = function(parentId, reply) {
    // Check if this is being called from a modal context
    const modalExists = document.querySelector(`[id^="commentModal-"]`);
    const modalParentExists = document.querySelector(`[id^="commentModal-"] [data-comment-id="${parentId}"]`);

    if (modalExists && modalParentExists) {
        // Don't add to main feed if modal is open and has this comment
        return;
    }

    // Call original function for regular comments only
    if (originalAddReplyToDOM) {
        originalAddReplyToDOM(parentId, reply);
    }
};

// Override submitCommentEdit to work with modals
const originalSubmitCommentEdit = window.submitCommentEdit;
window.submitCommentEdit = async function(form) {
    const commentId = form.dataset.commentId;
    const formData = new FormData(form);
    formData.append('_method', 'PUT');

    try {
        const response = await fetch(`/comments/${commentId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // Update comment content in all instances (both modal and main feed)
            const commentElements = document.querySelectorAll(`[data-comment-id="${commentId}"]`);
            commentElements.forEach(commentElement => {
                const commentContent = commentElement.querySelector('.comment-content p');
                if (commentContent) {
                    commentContent.innerHTML = data.comment.content.replace(/\n/g, '<br>');
                }
            });

            // Cancel edit mode for all instances
            cancelEditComment(commentId);
        } else {
            alert('Error updating comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error updating comment:', error);
        alert('Error updating comment. Please try again.');
    }
};

// Override updateCommentCount to work with modals
const originalUpdateCommentCount = window.updateCommentCount;
window.updateCommentCount = function(postId) {
    // Call original function
    if (originalUpdateCommentCount) {
        originalUpdateCommentCount(postId);
    }

    // Also update modal count
    const modalCountElement = document.getElementById(`modal-comments-count-${postId}`);
    if (modalCountElement) {
        const mainCountElement = document.getElementById(`comments-count-${postId}`);
        if (mainCountElement) {
            const countText = mainCountElement.textContent;
            modalCountElement.textContent = countText;
        }
    }
};

// Create modal comment HTML (adapted from existing createCommentHTML)
function createModalCommentHTML(comment, postId) {
    const timeAgo = formatTimeAgo ? formatTimeAgo(comment.created_at) : 'just now';
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;

    const currentUserAvatar = getCurrentUserAvatar();

    // Check if current user can edit/delete this comment
    const currentUserId = getCurrentUserId();
    const isCurrentUserAdmin = () => {
        const userRoleMeta = document.querySelector('meta[name="user-role"]');
        return userRoleMeta ? userRoleMeta.getAttribute('content') === 'admin' : false;
    };

    // Convert both to strings for comparison to handle type mismatches
    const canEditDelete = currentUserId && (
        String(currentUserId) === String(comment.user.id) ||
        isCurrentUserAdmin()
    );

    // Additional fallback: check by username if user ID comparison fails
    let showDropdown = canEditDelete;

    if (!currentUserId || currentUserId === 'current-user-fallback') {
        // If we can't get current user ID, try to match by username
        const currentUserName = document.querySelector('meta[name="user-name"]')?.getAttribute('content');
        if (currentUserName && comment.user.name === currentUserName) {
            showDropdown = true;
        }
    }

    return `
        <div class="comment-item py-4 px-4 hover:bg-gray-50 transition-colors duration-150" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="h-10 w-10 rounded-full ring-1 ring-gray-300 shadow-sm"
                         src="${userAvatar}"
                         alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-100 rounded-xl p-4 shadow-sm border border-gray-200">
                        <div class="flex items-center space-x-2 mb-2">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                            <span class="text-xs text-gray-500">${timeAgo}</span>
                            ${showDropdown ? `
                            <div class="relative ml-auto">
                                <button onclick="toggleCommentDropdown(${comment.id})" class="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-200">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                    </svg>
                                </button>
                                <div id="comment-dropdown-${comment.id}" class="hidden absolute right-0 top-8 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                                    <button onclick="editComment(${comment.id}); hideCommentDropdown(${comment.id})"
                                            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                        <span>Edit</span>
                                    </button>
                                    <button onclick="deleteComment(${comment.id}); hideCommentDropdown(${comment.id})"
                                            class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        <span>Delete</span>
                                    </button>
                                </div>
                            </div>
                            ` : ''}
                        </div>

                        <div class="comment-content">
                            <p class="text-gray-800 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>

                        <!-- Edit form (hidden by default) -->
                        <div class="comment-edit-form hidden mt-3">
                            <form class="edit-comment-form" data-comment-id="${comment.id}">
                                <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                                <input type="hidden" name="_method" value="PUT">
                                <textarea name="content" rows="2"
                                          class="w-full bg-gray-50 text-gray-900 border border-gray-300 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">${comment.content}</textarea>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="cancelEditComment(${comment.id})"
                                            class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                    <button type="submit"
                                            class="px-3 py-1.5 text-sm bg-custom-green text-white rounded-md hover:bg-green-600 transition-colors">Save</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Comment Actions -->
                    <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                        ${FacebookReactionSystem.generateReactionHTML(comment.id, 'comment', comment.user_reaction, comment.reaction_counts, true)}

                        <button onclick="showReplyForm(${comment.id})" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors group">
                            <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                            </svg>
                            <span class="font-medium">Reply</span>
                        </button>
                    </div>

                    <!-- Reply Form (hidden by default) -->
                    <div class="reply-form hidden mt-3 ml-4" id="reply-form-${comment.id}">
                        <form class="comment-form" data-post-id="${postId}" data-parent-id="${comment.id}">
                            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                            <div class="flex space-x-3">
                                <div class="flex-shrink-0">
                                    <img class="h-8 w-8 rounded-full ring-1 ring-gray-300 shadow-sm"
                                         src="${currentUserAvatar}"
                                         alt="Your avatar">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="relative">
                                        <textarea name="content" rows="1"
                                                  placeholder="Write a reply..."
                                                  class="w-full px-3 py-2 bg-gray-50 text-gray-900 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm hover:bg-gray-100 transition-colors duration-200"
                                                  required></textarea>
                                    </div>
                                    <div class="mt-2 flex justify-end space-x-2">
                                        <button type="button" onclick="hideReplyForm(${comment.id})"
                                                class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                        <button type="submit"
                                                class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                            Reply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Replies Section -->
                    ${comment.replies && comment.replies.length > 0 ? `
                    <div class="mt-3">
                        <button onclick="toggleModalReplies(${comment.id})"
                                class="flex items-center space-x-2 text-gray-600 hover:text-gray-800 text-sm font-medium transition-colors ml-2"
                                id="modal-view-replies-btn-${comment.id}">
                            <svg class="w-4 h-4 transform transition-transform" id="modal-replies-arrow-${comment.id}" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                            </svg>
                            <span id="modal-replies-text-${comment.id}">
                                View ${comment.replies.length} ${comment.replies.length === 1 ? 'reply' : 'replies'}
                            </span>
                        </button>
                        <div class="hidden mt-3 ml-4 space-y-3 border-l-2 border-gray-600 pl-4" id="modal-replies-list-${comment.id}">
                            ${comment.replies.map(reply => createModalCommentHTML(reply, postId)).join('')}
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}



// Submit comment in modal
async function submitModalComment(form) {
    const formData = new FormData(form);
    const postId = form.dataset.postId;
    const parentId = form.dataset.parentId;

    if (parentId) {
        formData.append('parent_id', parentId);
    }

    try {
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        const response = await fetch(`/posts/${postId}/comments`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': csrfToken
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            // Clear the form
            form.reset();

            // Hide submit button
            const submitBtn = form.querySelector('[id^="modal-comment-submit-btn-"]');
            if (submitBtn) {
                submitBtn.style.opacity = '0';
            }

            // Hide reply form if it was a reply
            if (parentId) {
                hideReplyForm(parentId);
                // Add reply to the modal only (don't call global addReplyToDOM)
                addModalReply(parentId, data.comment);
            } else {
                // Add new main comment to the comments list
                addCommentToDOM(postId, data.comment);
            }

            // Update comment count
            updateCommentCount(postId);

            // Trigger real-time summary update for both main feed and modal
            if (window.postSummaryUpdater) {
                window.postSummaryUpdater.onCommentChange(postId);
            }

            // Trigger real-time summary update
            if (window.postSummaryUpdater) {
                window.postSummaryUpdater.onCommentChange(postId);
            }
        } else {
            alert('Error posting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error submitting modal comment:', error);
        alert('Error posting comment. Please try again.');
    }
}

// Helper function to get current user avatar
function getCurrentUserAvatar() {
    const userAvatarElement = document.querySelector('meta[name="user-avatar"]');
    if (userAvatarElement) {
        return userAvatarElement.getAttribute('content');
    }

    // Fallback: try to get from any existing user avatar in the page
    const existingAvatar = document.querySelector('img[alt*="' + (document.querySelector('meta[name="user-name"]')?.getAttribute('content') || '') + '"]');
    if (existingAvatar) {
        return existingAvatar.src;
    }

    // Final fallback
    const userName = document.querySelector('meta[name="user-name"]')?.getAttribute('content') || 'User';
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&color=7BC74D&background=EEEEEE`;
}

// Helper function to get current user ID
function getCurrentUserId() {
    const userIdMeta = document.querySelector('meta[name="user-id"]');
    if (userIdMeta) {
        return userIdMeta.getAttribute('content');
    }

    // Fallback: try to get from comment form data attributes or other sources
    const commentForm = document.querySelector('.comment-form');
    if (commentForm && commentForm.dataset.userId) {
        return commentForm.dataset.userId;
    }

    // Another fallback: check if we can get it from any existing comment edit button
    const editButton = document.querySelector('[onclick*="editComment"]');
    if (editButton) {
        // If user can see edit buttons, they're likely the comment author
        // This is a weak fallback but better than nothing
        return 'current-user-fallback';
    }

    return null;
}

// Helper function to format time ago (fallback if not available from comments.js)
if (typeof formatTimeAgo === 'undefined') {
    function formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        return `${Math.floor(diffInSeconds / 86400)} days ago`;
    }
}

// Toggle comment dropdown - Enhanced for modal compatibility
function toggleCommentDropdown(commentId) {
    // Try to find dropdown in both modal and main feed contexts
    let dropdown = document.getElementById(`comment-dropdown-${commentId}`);

    // If not found, try to find it within any open modal
    if (!dropdown) {
        const openModals = document.querySelectorAll('[id^="commentModal-"]:not(.hidden), [id^="shareCommentModal-"]:not(.hidden)');
        openModals.forEach(modal => {
            const modalDropdown = modal.querySelector(`#comment-dropdown-${commentId}`);
            if (modalDropdown) {
                dropdown = modalDropdown;
            }
        });
    }

    if (dropdown) {
        dropdown.classList.toggle('hidden');

        // Close other dropdowns in the same context (modal or main feed)
        const isInModal = dropdown.closest('[id^="commentModal-"], [id^="shareCommentModal-"]');
        const contextSelector = isInModal ?
            '[id^="commentModal-"] [id^="comment-dropdown-"], [id^="shareCommentModal-"] [id^="comment-dropdown-"]' :
            '[id^="comment-dropdown-"]:not([id^="commentModal-"] [id^="comment-dropdown-"]):not([id^="shareCommentModal-"] [id^="comment-dropdown-"])';

        document.querySelectorAll(contextSelector).forEach(otherDropdown => {
            if (otherDropdown.id !== `comment-dropdown-${commentId}`) {
                otherDropdown.classList.add('hidden');
            }
        });
    }
}

// Hide comment dropdown - Enhanced for modal compatibility
function hideCommentDropdown(commentId) {
    // Try to find dropdown in both modal and main feed contexts
    let dropdown = document.getElementById(`comment-dropdown-${commentId}`);

    // If not found, try to find it within any open modal
    if (!dropdown) {
        const openModals = document.querySelectorAll('[id^="commentModal-"]:not(.hidden), [id^="shareCommentModal-"]:not(.hidden)');
        openModals.forEach(modal => {
            const modalDropdown = modal.querySelector(`#comment-dropdown-${commentId}`);
            if (modalDropdown) {
                dropdown = modalDropdown;
            }
        });
    }

    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}

// Ensure edit and delete functions work in modal
window.editComment = function(commentId) {
    // Try to find elements in both modal and main feed
    const commentElements = document.querySelectorAll(`[data-comment-id="${commentId}"]`);

    commentElements.forEach(commentElement => {
        const commentContent = commentElement.querySelector('.comment-content');
        const editForm = commentElement.querySelector('.comment-edit-form');

        if (commentContent && editForm) {
            commentContent.classList.add('hidden');
            editForm.classList.remove('hidden');
            const textarea = editForm.querySelector('textarea');

            if (textarea) {
                textarea.focus();
                // Auto-resize if in modal
                if (commentElement.closest('[id^="commentModal-"]')) {
                    autoResizeTextarea(textarea);
                }
            }
        }
    });
};

window.cancelEditComment = function(commentId) {
    // Try to find elements in both modal and main feed
    const commentElements = document.querySelectorAll(`[data-comment-id="${commentId}"]`);

    commentElements.forEach(commentElement => {
        const commentContent = commentElement.querySelector('.comment-content');
        const editForm = commentElement.querySelector('.comment-edit-form');

        if (commentContent && editForm) {
            commentContent.classList.remove('hidden');
            editForm.classList.add('hidden');
        }
    });
};

window.deleteComment = async function(commentId) {
    if (!confirm('Are you sure you want to delete this comment?')) {
        return;
    }

    try {
        const response = await fetch(`/comments/${commentId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Remove comment from DOM (both modal and main feed)
            const commentElements = document.querySelectorAll(`[data-comment-id="${commentId}"]`);
            let postId = null;

            commentElements.forEach(element => {
                // Try to extract postId from the comment element or its parent
                const postCard = element.closest('[data-post-id]');
                const modal = element.closest('[id^="commentModal-"]');

                if (postCard) {
                    postId = postCard.dataset.postId;
                } else if (modal) {
                    // Extract postId from modal ID
                    const modalIdMatch = modal.id.match(/commentModal-(\d+)/);
                    if (modalIdMatch) {
                        postId = modalIdMatch[1];
                    }
                }

                element.remove();
            });

            // Trigger real-time summary update if we have the postId
            if (postId && window.postSummaryUpdater) {
                window.postSummaryUpdater.onCommentChange(postId);
            }
        } else {
            alert('Error deleting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting comment:', error);
        alert('Error deleting comment. Please try again.');
    }
};

// Enhanced hideReplyForm function for modal compatibility
function hideReplyForm(commentId) {
    try {
        // Try to find reply forms in both modal and main feed contexts
        const replyForms = document.querySelectorAll(`#reply-form-${commentId}`);

        if (replyForms.length === 0) {
            return;
        }

        replyForms.forEach(replyForm => {
            if (replyForm) {
                // Add the hidden class (primary method)
                replyForm.classList.add('hidden');

                // Clear any inline styles that might override the hidden class
                replyForm.style.removeProperty('display');
                replyForm.style.removeProperty('visibility');
                replyForm.style.removeProperty('opacity');
                replyForm.style.removeProperty('height');
                replyForm.style.removeProperty('max-height');
                replyForm.style.removeProperty('overflow');
                replyForm.style.removeProperty('margin');
                replyForm.style.removeProperty('padding');

                // Reset the form
                const form = replyForm.querySelector('form');
                if (form) {
                    form.reset();
                }

                // Clear the textarea specifically
                const textarea = replyForm.querySelector('textarea');
                if (textarea) {
                    textarea.value = '';
                    // Reset textarea height if it was auto-resized
                    textarea.style.height = 'auto';
                }
            }
        });
    } catch (error) {
        console.error('Error in hideReplyForm:', error);
    }
}

// Test function to check if JavaScript is working
window.testJS = function() {
    alert('JavaScript is working!');
};

// Make sure modal functions are globally accessible
window.hideReplyForm = hideReplyForm;
window.toggleModalReplies = toggleModalReplies;
window.hideCommentDropdown = hideCommentDropdown;
window.toggleCommentDropdown = toggleCommentDropdown;

// Override toggleCommentLike for modal compatibility
const originalToggleCommentLike = window.toggleCommentLike;
window.toggleCommentLike = async function(commentId) {
    try {
        const response = await fetch(`/comments/${commentId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Update all instances of this comment (both in modal and main feed)
            const likeBtns = document.querySelectorAll(`#comment-like-btn-${commentId}`);
            const likeCounts = document.querySelectorAll(`#comment-like-count-${commentId}`);

            likeBtns.forEach(likeBtn => {
                const heartIcon = likeBtn.querySelector('svg');

                // Add animation effect
                likeBtn.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    likeBtn.style.transform = 'scale(1)';
                }, 150);

                if (data.liked) {
                    likeBtn.classList.add('text-red-600');
                    likeBtn.classList.remove('text-gray-500');
                    if (heartIcon) {
                        heartIcon.classList.add('text-red-600', 'fill-current');
                        heartIcon.setAttribute('fill', 'currentColor');
                        // Add a subtle bounce animation
                        heartIcon.style.animation = 'heartBeat 0.6s ease-in-out';
                        setTimeout(() => {
                            heartIcon.style.animation = '';
                        }, 600);
                    }
                } else {
                    likeBtn.classList.remove('text-red-600');
                    likeBtn.classList.add('text-gray-500');
                    if (heartIcon) {
                        heartIcon.classList.remove('text-red-600', 'fill-current');
                        heartIcon.setAttribute('fill', 'none');
                    }
                }
            });

            // Update like counts
            likeCounts.forEach(likeCount => {
                likeCount.textContent = data.likes_count;
            });
        }
    } catch (error) {
        console.error('Error toggling comment like:', error);
    }
};

// Override showReplyForm for modal compatibility
const originalShowReplyForm = window.showReplyForm;
window.showReplyForm = function(commentId) {
    // Try to find reply forms in both modal and main feed
    const replyForms = document.querySelectorAll(`#reply-form-${commentId}`);

    replyForms.forEach(replyForm => {
        if (replyForm) {
            replyForm.classList.remove('hidden');
            const textarea = replyForm.querySelector('textarea');

            if (textarea) {
                textarea.focus();
                // Auto-resize if in modal
                if (replyForm.closest('[id^="commentModal-"]')) {
                    autoResizeTextarea(textarea);
                }
            }
        }
    });
};

// Override global comment dropdown functions to ensure modal compatibility
window.toggleCommentDropdown = toggleCommentDropdown;
window.hideCommentDropdown = hideCommentDropdown;

// Initialize sort dropdown functionality
function initializeSortDropdown(postId) {
    const sortDropdown = document.getElementById(`comment-sort-${postId}`);

    if (sortDropdown && !sortDropdown.hasAttribute('data-initialized')) {
    
        sortDropdown.setAttribute('data-initialized', 'true');
        sortDropdown.addEventListener('change', function() {
            const sortBy = this.value;
            loadSortedComments(postId, sortBy);
        });
    }
}

// Load comments with sorting
async function loadSortedComments(postId, sortBy) {
    // First try to find the modal's comments container
    const modal = document.getElementById(`commentModal-${postId}`);
    let commentsContainer = null;

    if (modal) {
        commentsContainer = modal.querySelector(`#comments-list-${postId}`);
    }

    // Fallback to main page container if modal not found
    if (!commentsContainer) {
        commentsContainer = document.getElementById(`comments-list-${postId}`);
    }

    if (!commentsContainer) return;

    // Show loading state
    commentsContainer.innerHTML = `
        <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-custom-green"></div>
            <span class="ml-3 text-gray-400">Loading comments...</span>
        </div>
    `;

    try {
        const response = await fetch(`/posts/${postId}/comments?sort=${sortBy}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            // Clear the container
            commentsContainer.innerHTML = '';

            if (data.comments.length === 0) {
                // Show no comments message
                commentsContainer.innerHTML = `
                    <div class="no-comments text-gray-600 text-center py-12 px-4">
                        <div class="max-w-sm mx-auto">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-700 mb-2">No comments yet</h3>
                            <p class="text-gray-500">Be the first to share what you think!</p>
                        </div>
                    </div>
                `;
            } else {
                // Add sorted comments
                data.comments.forEach(comment => {
                    const commentHTML = createModalCommentHTML(comment, postId);
                    if (commentHTML) {
                        commentsContainer.insertAdjacentHTML('beforeend', commentHTML);
                    }
                });
            }
        } else {
            throw new Error(data.message || 'Failed to load comments');
        }
    } catch (error) {
        console.error('Error loading sorted comments:', error);
        commentsContainer.innerHTML = `
            <div class="text-red-400 text-center py-8">
                <p>Error loading comments. Please try again.</p>
                <button onclick="loadSortedComments(${postId}, '${sortBy}')" class="mt-2 text-custom-green hover:text-custom-second-darkest">
                    Retry
                </button>
            </div>
        `;
    }
}



// Override toggleLike for modal compatibility
const originalToggleLike = window.toggleLike;
window.toggleLike = async function(postId) {
    try {
        const response = await fetch(`/posts/${postId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Update both main feed and modal like buttons
            const likeBtns = [
                document.getElementById(`like-btn-${postId}`),
                document.getElementById(`like-btn-${postId}-modal`)
            ].filter(btn => btn !== null);

            const likeCounts = [
                document.getElementById(`like-count-${postId}`),
                document.getElementById(`modal-likes-count-${postId}`)
            ].filter(count => count !== null);

            const commentCounts = [
                document.getElementById(`comments-count-${postId}`),
                document.getElementById(`modal-comments-count-${postId}`)
            ].filter(count => count !== null);

            // Update like buttons
            likeBtns.forEach(likeBtn => {
                const heartIcon = likeBtn.querySelector('svg');

                if (data.liked) {
                    // Remove gray classes first
                    heartIcon.classList.remove('text-gray-400', 'text-gray-500');
                    // Add red color classes to SVG
                    if (likeBtn.id.includes('-modal')) {
                        heartIcon.classList.add('text-red-500', 'fill-current');
                    } else {
                        heartIcon.classList.add('text-red-600', 'fill-current');
                    }
                    heartIcon.setAttribute('fill', 'currentColor');
                    // Add animation for better UX
                    heartIcon.style.animation = 'heartBeat 0.6s ease-in-out';
                    setTimeout(() => {
                        heartIcon.style.animation = '';
                    }, 600);
                } else {
                    // Remove red color classes
                    heartIcon.classList.remove('text-red-500', 'text-red-600', 'fill-current');
                    // Add back gray color classes explicitly
                    if (likeBtn.id.includes('-modal')) {
                        heartIcon.classList.add('text-gray-400');
                    } else {
                        heartIcon.classList.add('text-gray-500');
                    }
                    heartIcon.setAttribute('fill', 'none');
                }
            });

            // Update like counts
            likeCounts.forEach(likeCount => {
                if (likeCount.id.includes('modal-likes-count')) {
                    // Modal format: just the number
                    likeCount.textContent = data.likes_count;
                } else {
                    // Main feed format: "X likes"
                    likeCount.textContent = `${data.likes_count} likes`;
                }
            });

            // Show/hide the like count section in modal
            const modalLikeSection = document.getElementById(`modal-likes-section-${postId}`);
            if (modalLikeSection) {
                if (data.likes_count > 0) {
                    modalLikeSection.style.display = 'flex';
                } else {
                    modalLikeSection.style.display = 'none';
                }
            }
        }
    } catch (error) {
        console.error('Error toggling like:', error);
    }
};

/**
 * Sync modal reaction button with main feed reaction state
 */
function syncModalReactionWithMainFeed(postId) {
    // Find the main feed reaction button for this post
    const mainFeedReactionBtn = document.querySelector(`[data-post-id="${postId}"] .reaction-btn`);
    const modalReactionBtn = document.querySelector(`#commentModal-${postId} .reaction-btn`);

    if (!mainFeedReactionBtn || !modalReactionBtn) {
        console.log('Could not find reaction buttons to sync');
        return;
    }

    // Get the current reaction state from main feed
    const currentReaction = mainFeedReactionBtn.dataset.currentReaction;
    const mainFeedHtml = mainFeedReactionBtn.innerHTML;
    const mainFeedClasses = mainFeedReactionBtn.className;

    // Update modal reaction button to match main feed
    modalReactionBtn.dataset.currentReaction = currentReaction;
    modalReactionBtn.innerHTML = mainFeedHtml;
    modalReactionBtn.className = mainFeedClasses;

    // console.log(`Synced modal reaction with main feed: ${currentReaction || 'none'}`);
}

/**
 * Sync main feed reaction button with modal reaction state
 */
function syncMainFeedReactionWithModal(postId) {
    // Find the modal and main feed reaction buttons for this post
    const modalReactionBtn = document.querySelector(`#commentModal-${postId} .reaction-btn`);
    const mainFeedReactionBtn = document.querySelector(`[data-post-id="${postId}"] .reaction-btn`);

    if (!modalReactionBtn || !mainFeedReactionBtn) {
        return;
    }

    // Get the current reaction state from modal
    const currentReaction = modalReactionBtn.dataset.currentReaction;
    const modalHtml = modalReactionBtn.innerHTML;
    const modalClasses = modalReactionBtn.className;

    // Update main feed reaction button to match modal
    mainFeedReactionBtn.dataset.currentReaction = currentReaction;
    mainFeedReactionBtn.innerHTML = modalHtml;
    mainFeedReactionBtn.className = modalClasses;

    // console.log(`Synced main feed reaction with modal: ${currentReaction || 'none'}`);
}

/**
 * Sync share modal reaction button with main feed reaction state
 */
function syncShareModalReactionWithMainFeed(shareId) {
    console.log('syncShareModalReactionWithMainFeed called for shareId:', shareId);

    // Find the main feed reaction button for this share (more specific selector)
    const mainFeedReactionBtn = document.querySelector(`[data-share-id="${shareId}"] .reaction-btn[data-target-type="share"]`);
    const modalReactionBtn = document.querySelector(`#shareCommentModal-${shareId} .reaction-btn[data-target-type="share"]`);

    console.log('Main feed reaction btn:', mainFeedReactionBtn);
    console.log('Modal reaction btn:', modalReactionBtn);

    if (!mainFeedReactionBtn || !modalReactionBtn) {
        console.log('Could not find both reaction buttons for sync');
        console.log('Main feed selector:', `[data-share-id="${shareId}"] .reaction-btn[data-target-type="share"]`);
        console.log('Modal selector:', `#shareCommentModal-${shareId} .reaction-btn[data-target-type="share"]`);
        return;
    }

    // Get the current reaction state from main feed
    const currentReaction = mainFeedReactionBtn.dataset.currentReaction || '';
    const mainFeedHtml = mainFeedReactionBtn.innerHTML;
    const mainFeedClasses = mainFeedReactionBtn.className;

    console.log('Syncing reaction state:', currentReaction);

    // Update modal reaction button to match main feed
    modalReactionBtn.dataset.currentReaction = currentReaction;
    modalReactionBtn.innerHTML = mainFeedHtml;
    modalReactionBtn.className = mainFeedClasses;

    console.log('Modal reaction button updated successfully');
}

/**
 * Sync main feed reaction button with share modal reaction state
 */
function syncMainFeedReactionWithShareModal(shareId) {
    console.log('syncMainFeedReactionWithShareModal called for shareId:', shareId);

    // Find the modal and main feed reaction buttons for this share (more specific selectors)
    const modalReactionBtn = document.querySelector(`#shareCommentModal-${shareId} .reaction-btn[data-target-type="share"]`);
    const mainFeedReactionBtn = document.querySelector(`[data-share-id="${shareId}"] .reaction-btn[data-target-type="share"]`);

    console.log('Modal reaction btn:', modalReactionBtn);
    console.log('Main feed reaction btn:', mainFeedReactionBtn);

    if (!modalReactionBtn || !mainFeedReactionBtn) {
        console.log('Could not find both reaction buttons for sync');
        console.log('Modal selector:', `#shareCommentModal-${shareId} .reaction-btn[data-target-type="share"]`);
        console.log('Main feed selector:', `[data-share-id="${shareId}"] .reaction-btn[data-target-type="share"]`);
        return;
    }

    // Get the current reaction state from modal
    const currentReaction = modalReactionBtn.dataset.currentReaction || '';
    const modalHtml = modalReactionBtn.innerHTML;
    const modalClasses = modalReactionBtn.className;

    console.log('Syncing reaction state from modal:', currentReaction);

    // Update main feed reaction button to match modal
    mainFeedReactionBtn.dataset.currentReaction = currentReaction;
    mainFeedReactionBtn.innerHTML = modalHtml;
    mainFeedReactionBtn.className = modalClasses;

    console.log('Main feed reaction button updated successfully');
}

// Override addShareCommentToDOM to work with modals
const originalAddShareCommentToDOM = window.addShareCommentToDOM;
window.addShareCommentToDOM = function(shareId, comment) {
    console.log('Override addShareCommentToDOM called with:', { shareId, comment });

    // Check if share modal is open first
    const modal = document.getElementById(`shareCommentModal-${shareId}`);
    const isModalOpen = modal && !modal.classList.contains('hidden');

    console.log('Share modal open:', isModalOpen);

    if (isModalOpen) {
        // If modal is open, add to modal using the correct selector
        const modalCommentsList = modal.querySelector(`#modal-share-comments-list-${shareId}`);
        console.log('Modal comments list:', modalCommentsList);

        if (modalCommentsList) {
            // Remove "no comments" message if it exists
            const noComments = modalCommentsList.querySelector('.no-comments');
            if (noComments) {
                console.log('Removing no-comments message from modal');
                noComments.remove();
            }

            // Use modal-specific styling
            const commentHTML = createModalShareCommentHTML(comment, shareId);
            modalCommentsList.insertAdjacentHTML('afterbegin', commentHTML);
            console.log('Comment added to modal');

            // Initialize reactions for the new comment
            if (window.facebookReactionSystem) {
                setTimeout(() => {
                    window.facebookReactionSystem.createReactionPopups();
                }, 100);
            }
        }

        // Also update main feed if it exists
        const mainFeedCommentsList = document.querySelector(`[data-share-id="${shareId}"] #share-comments-list-${shareId}`);
        if (mainFeedCommentsList) {
            const mainNoComments = mainFeedCommentsList.querySelector('.no-comments');
            if (mainNoComments) {
                mainNoComments.remove();
            }
            const mainCommentHTML = createShareCommentHTML(comment, shareId);
            mainFeedCommentsList.insertAdjacentHTML('afterbegin', mainCommentHTML);
        }
    } else {
        // Call original function for regular share comments
        if (originalAddShareCommentToDOM) {
            originalAddShareCommentToDOM(shareId, comment);
        }
    }
};

// Override addShareReplyToDOM to work with modals
const originalAddShareReplyToDOM = window.addShareReplyToDOM;
window.addShareReplyToDOM = function(parentId, reply) {
    console.log('Override addShareReplyToDOM called with:', { parentId, reply });

    // Check if we're in a modal context
    const modalParentExists = document.querySelector(`[id^="shareCommentModal-"] [data-comment-id="${parentId}"]`);

    if (modalParentExists) {
        console.log('Adding reply to modal');
        // Find the parent comment in the modal
        const modalParentComment = document.querySelector(`[id^="shareCommentModal-"] [data-comment-id="${parentId}"]`);

        if (modalParentComment) {
            // Check if the modal has a proper replies section structure
            let repliesList = modalParentComment.querySelector(`#modal-share-replies-list-${parentId}`);

            if (!repliesList) {
                // Create the replies section if it doesn't exist
                const repliesSection = document.createElement('div');
                repliesSection.className = 'mt-3';
                repliesSection.innerHTML = `
                    <!-- View Replies Button -->
                    <button onclick="toggleModalShareReplies(${parentId})"
                            class="flex items-center space-x-2 text-gray-600 hover:text-gray-800 text-sm font-medium transition-colors ml-2"
                            id="modal-share-view-replies-btn-${parentId}">
                        <svg class="w-4 h-4 transform transition-transform" id="modal-share-replies-arrow-${parentId}" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                        </svg>
                        <span id="modal-share-replies-text-${parentId}">
                            View 1 reply
                        </span>
                    </button>

                    <!-- Replies List (hidden by default) -->
                    <div class="hidden mt-3 ml-4 space-y-3 border-l-2 border-gray-600 pl-4" id="modal-share-replies-list-${parentId}">
                    </div>
                `;

                modalParentComment.appendChild(repliesSection);
                repliesList = modalParentComment.querySelector(`#modal-share-replies-list-${parentId}`);
            }

            if (repliesList) {
                // Get shareId from the modal context
                const modal = modalParentComment.closest('[id^="shareCommentModal-"]');
                const shareId = modal ? modal.id.replace('shareCommentModal-', '') : reply.commentable_id;

                const replyHTML = createModalShareCommentHTML(reply, shareId);
                repliesList.insertAdjacentHTML('beforeend', replyHTML);

                // Initialize reactions
                if (window.facebookReactionSystem) {
                    setTimeout(() => {
                        window.facebookReactionSystem.createReactionPopups();
                    }, 100);
                }

                // Update replies count and show replies section
                const repliesBtn = modalParentComment.querySelector(`#modal-share-view-replies-btn-${parentId}`);
                const repliesText = modalParentComment.querySelector(`#modal-share-replies-text-${parentId}`);

                if (repliesBtn && repliesText) {
                    const currentCount = repliesList.children.length;
                    repliesText.textContent = `View ${currentCount} ${currentCount === 1 ? 'reply' : 'replies'}`;

                    // Show replies if they were hidden
                    if (repliesList.classList.contains('hidden')) {
                        repliesList.classList.remove('hidden');
                        const arrow = modalParentComment.querySelector(`#modal-share-replies-arrow-${parentId}`);
                        if (arrow) {
                            arrow.style.transform = 'rotate(180deg)';
                        }
                        repliesText.textContent = 'Hide replies';
                    }
                }
            }
        }

        // Also add to main feed if it exists
        const mainParentComment = document.querySelector(`[data-share-id] [data-comment-id="${parentId}"]`);
        if (mainParentComment) {
            let mainNestedComments = mainParentComment.querySelector('.nested-comments');
            if (!mainNestedComments) {
                const commentActions = mainParentComment.querySelector('.comment-actions');
                if (commentActions) {
                    mainNestedComments = document.createElement('div');
                    mainNestedComments.className = 'nested-comments ml-12 mt-3 space-y-3';
                    commentActions.insertAdjacentElement('afterend', mainNestedComments);
                }
            }

            if (mainNestedComments) {
                const shareId = reply.commentable_id;
                const mainReplyHTML = createShareCommentHTML(reply, shareId, true);
                mainNestedComments.insertAdjacentHTML('beforeend', mainReplyHTML);
            }
        }
    } else {
        // Call original function for regular share replies
        if (originalAddShareReplyToDOM) {
            originalAddShareReplyToDOM(parentId, reply);
        }
    }
};

// Make sync functions globally available
window.syncModalReactionWithMainFeed = syncModalReactionWithMainFeed;
window.syncMainFeedReactionWithModal = syncMainFeedReactionWithModal;
window.syncShareModalReactionWithMainFeed = syncShareModalReactionWithMainFeed;
window.syncMainFeedReactionWithShareModal = syncMainFeedReactionWithShareModal;

// Initialize modal functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textareas in modals and show/hide submit button
    document.addEventListener('input', function(e) {
        if (e.target.tagName === 'TEXTAREA' && e.target.closest('[id^="commentModal-"], [id^="shareCommentModal-"]')) {
            autoResizeTextarea(e.target);

            // Show/hide submit button based on content
            const form = e.target.closest('form');
            if (form) {
                const postId = form.dataset.postId;
                const shareId = form.dataset.shareId;
                let submitBtnId;

                if (postId) {
                    submitBtnId = `modal-comment-submit-btn-${postId}`;
                } else if (shareId) {
                    submitBtnId = `modal-share-comment-submit-btn-${shareId}`;
                }

                const submitBtn = document.getElementById(submitBtnId);
                if (submitBtn) {
                    if (e.target.value.trim()) {
                        submitBtn.style.opacity = '1';
                    } else {
                        submitBtn.style.opacity = '0';
                    }
                }
            }
        }
    });

    // Handle Enter key for comment submission in modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey && e.target.tagName === 'TEXTAREA') {
            const form = e.target.closest('.comment-form, .share-comment-form');
            if (form && e.target.value.trim() && form.closest('[id^="commentModal-"], [id^="shareCommentModal-"]')) {
                e.preventDefault();
                // Directly call the submit function instead of dispatching event
                if (form.classList.contains('comment-form')) {
                    submitModalComment(form);
                } else if (form.classList.contains('share-comment-form')) {
                    submitShareComment(form);
                }
            }
        }
    });

    // Handle form submissions in modals
    document.addEventListener('submit', function(e) {
        // Check if this is a modal form first
        const isModalForm = e.target.closest('[id^="commentModal-"], [id^="shareCommentModal-"]');

        if (isModalForm) {
            e.preventDefault();
            e.stopPropagation(); // Prevent other handlers from running

            if (e.target.classList.contains('comment-form')) {
                submitModalComment(e.target);
            } else if (e.target.classList.contains('edit-comment-form')) {
                submitCommentEdit(e.target);
            } else if (e.target.classList.contains('share-comment-form')) {
                submitShareComment(e.target);
            }
        }
    }, true); // Use capture phase to handle before other handlers

    // Handle cancel button clicks using event delegation as backup
    document.addEventListener('click', function(e) {
        // Check if clicked element is a cancel button in a reply form
        if (e.target.type === 'button' &&
            e.target.textContent.trim() === 'Cancel' &&
            e.target.closest('.reply-form')) {

            e.preventDefault();
            e.stopPropagation();

            const replyForm = e.target.closest('.reply-form');
            if (replyForm && replyForm.id) {
                const commentId = replyForm.id.replace('reply-form-', '');
                hideReplyForm(commentId);
            }
        }
    });

    // Handle comment dropdown clicks with event delegation for dynamically added comments
    document.addEventListener('click', function(e) {
        // Handle dropdown toggle buttons
        const dropdownButton = e.target.closest('button[onclick*="toggleCommentDropdown"]');
        if (dropdownButton) {
            // Extract comment ID from onclick attribute
            const onclickAttr = dropdownButton.getAttribute('onclick');
            const commentIdMatch = onclickAttr.match(/toggleCommentDropdown\((\d+)\)/);
            if (commentIdMatch) {
                const commentId = commentIdMatch[1];
                e.preventDefault();
                e.stopPropagation();
                toggleCommentDropdown(parseInt(commentId));
                return;
            }
        }

        // Handle edit comment buttons in dropdowns
        const editButton = e.target.closest('button[onclick*="editComment"]');
        if (editButton) {
            const onclickAttr = editButton.getAttribute('onclick');
            const commentIdMatch = onclickAttr.match(/editComment\((\d+)\)/);
            if (commentIdMatch) {
                const commentId = commentIdMatch[1];
                e.preventDefault();
                e.stopPropagation();
                editComment(parseInt(commentId));
                hideCommentDropdown(parseInt(commentId));
                return;
            }
        }

        // Handle delete comment buttons in dropdowns
        const deleteButton = e.target.closest('button[onclick*="deleteComment"]');
        if (deleteButton) {
            const onclickAttr = deleteButton.getAttribute('onclick');
            const commentIdMatch = onclickAttr.match(/deleteComment\((\d+)\)/);
            if (commentIdMatch) {
                const commentId = commentIdMatch[1];
                e.preventDefault();
                e.stopPropagation();
                deleteComment(parseInt(commentId));
                hideCommentDropdown(parseInt(commentId));
                return;
            }
        }

        // Close dropdowns when clicking outside
        if (!e.target.closest('[id^="comment-dropdown-"]') && !e.target.closest('button[onclick*="toggleCommentDropdown"]')) {
            document.querySelectorAll('[id^="comment-dropdown-"]').forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        }
    });
});
