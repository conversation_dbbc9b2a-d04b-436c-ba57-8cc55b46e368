@props(['share'])

<!-- Share Comment Modal -->
<div id="shareCommentModal-{{ $share->id }}" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden z-50" onclick="closeShareCommentModal({{ $share->id }}, event)">
    <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-2xl flex flex-col overflow-hidden" style="height: 90vh; max-height: 90vh;" onclick="event.stopPropagation()">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
            <h3 class="text-lg font-medium text-gray-900">
                {{ $share->user->name }}'s shared post
            </h3>
            <button onclick="closeShareCommentModal({{ $share->id }})" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Left Side - Original Post Content -->
            <div class="w-1/2 border-r border-gray-200 flex flex-col">
                <!-- Share Message -->
                @if($share->message)
                    <div class="p-4 border-b border-gray-100 bg-gray-50">
                        <div class="flex items-center space-x-3 mb-3">
                            <img class="h-8 w-8 rounded-full" 
                                 src="{{ $share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                                 alt="{{ $share->user->name }}">
                            <div>
                                <div class="font-medium text-gray-900">{{ $share->user->name }}</div>
                                <div class="text-sm text-gray-500">{{ $share->created_at->diffForHumans() }}</div>
                            </div>
                        </div>
                        <p class="text-gray-700">{!! nl2br(e($share->message)) !!}</p>
                    </div>
                @endif

                <!-- Original Post Content -->
                <div class="flex-1 overflow-y-auto p-4">
                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <!-- Original Post Header -->
                        <div class="p-4 bg-gray-50 border-b border-gray-200">
                            <div class="flex items-center space-x-3">
                                @if($share->post->group)
                                    <!-- Group Post Header -->
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('groups.show', $share->post->group) }}">
                                            <img class="h-8 w-8 rounded-full"
                                                 src="{{ $share->post->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->group->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->group->name) . '&color=3B82F6&background=DBEAFE' }}"
                                                 alt="{{ $share->post->group->name }}">
                                        </a>
                                        <div class="relative -ml-1">
                                            <a href="{{ route('profile.user', $share->post->user) }}">
                                                <img class="h-6 w-6 rounded-full border-2 border-white"
                                                     src="{{ $share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                                     alt="{{ $share->post->user->name }}">
                                            </a>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2">
                                            <a href="{{ route('groups.show', $share->post->group) }}" class="font-medium text-gray-900 hover:text-custom-green">
                                                {{ $share->post->group->name }}
                                            </a>
                                        </div>
                                        <div class="flex items-center space-x-1 text-sm text-gray-500">
                                            <a href="{{ route('profile.user', $share->post->user) }}" class="hover:text-custom-green font-medium">
                                                {{ $share->post->user->name }}
                                            </a>
                                            <span>•</span>
                                            <span>{{ $share->post->published_at->diffForHumans() }}</span>
                                        </div>
                                    </div>
                                @elseif($share->post->organization)
                                    <!-- Organization Post Header -->
                                    <a href="{{ route('organizations.show', $share->post->organization) }}">
                                        <img class="h-8 w-8 rounded-full"
                                             src="{{ $share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=3B82F6&background=DBEAFE' }}"
                                             alt="{{ $share->post->organization->name }}">
                                    </a>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2">
                                            <a href="{{ route('organizations.show', $share->post->organization) }}" class="font-medium text-gray-900 hover:text-custom-green">
                                                {{ $share->post->organization->name }}
                                            </a>
                                            <span class="text-gray-500 text-sm">•</span>
                                            <a href="{{ route('profile.user', $share->post->user) }}" class="text-sm text-gray-600 hover:text-custom-green">
                                                by {{ $share->post->user->name }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $share->post->published_at->diffForHumans() }}
                                        </div>
                                    </div>
                                @else
                                    <!-- Regular User Post Header -->
                                    <a href="{{ route('profile.user', $share->post->user) }}">
                                        <img class="h-8 w-8 rounded-full"
                                             src="{{ $share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                             alt="{{ $share->post->user->name }}">
                                    </a>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2">
                                            <a href="{{ route('profile.user', $share->post->user) }}" class="font-medium text-gray-900 hover:text-custom-green">
                                                {{ $share->post->user->name }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $share->post->published_at->diffForHumans() }}
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Original Post Content -->
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $share->post->title }}</h3>
                            
                            @if($share->post->content)
                                <div class="text-gray-700 mb-3">
                                    <p>{!! nl2br(e($share->post->content)) !!}</p>
                                </div>
                            @endif

                            <!-- Post Images -->
                            @if($share->post->images && count($share->post->images) > 0)
                                <div class="mb-3">
                                    @if(count($share->post->images) == 1)
                                        <div class="rounded-lg overflow-hidden">
                                            <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0]) }}" 
                                                 alt="Post image" 
                                                 class="w-full h-64 object-cover">
                                        </div>
                                    @else
                                        <div class="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                                            @foreach(array_slice($share->post->images, 0, 4) as $index => $image)
                                                <div class="relative">
                                                    <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($image) }}" 
                                                         alt="Post image {{ $index + 1 }}" 
                                                         class="w-full h-32 object-cover">
                                                    @if($index == 3 && count($share->post->images) > 4)
                                                        <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                            <span class="text-white font-semibold">+{{ count($share->post->images) - 4 }}</span>
                                                        </div>
                                                    @endif
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            @endif

                            <!-- Post Type Badge -->
                            @if($share->post->type !== 'general')
                                <div class="mb-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($share->post->type === 'announcement') bg-blue-100 text-blue-800
                                        @elseif($share->post->type === 'event') bg-green-100 text-green-800
                                        @elseif($share->post->type === 'job') bg-purple-100 text-purple-800
                                        @elseif($share->post->type === 'scholarship') bg-yellow-100 text-yellow-800
                                        @else bg-gray-100 text-gray-800
                                        @endif">
                                        {{ ucfirst($share->post->type) }}
                                    </span>
                                </div>
                            @endif
                        </div>

                        <!-- Original Post Interaction Stats -->
                        <div class="px-4 py-2 border-t border-gray-100">
                            <div class="flex items-center justify-between text-sm text-gray-500">
                                <div class="flex items-center space-x-4">
                                    @if($share->post->reactions && $share->post->reactions->count() > 0)
                                        <span class="flex items-center space-x-1">
                                            @php
                                                $reactionCounts = $share->post->reactions->groupBy('type')->map->count()->toArray();
                                                arsort($reactionCounts);
                                                $topReactions = array_slice($reactionCounts, 0, 3, true);
                                            @endphp
                                            @foreach($topReactions as $type => $count)
                                                @php $details = \App\Models\Reaction::getReactionDetails($type); @endphp
                                                <img src="{{ $details['emoji'] }}" alt="{{ $details['label'] }}" class="w-4 h-4">
                                            @endforeach
                                            <span>{{ $share->post->reactions->count() }}</span>
                                        </span>
                                    @endif
                                    
                                    @if($share->post->comments->count() > 0)
                                        <span>{{ $share->post->comments->count() }} comments</span>
                                    @endif
                                    
                                    @if($share->post->shares->count() > 0)
                                        <span>{{ $share->post->shares->count() }} shares</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Original Post Action Buttons -->
                        <div class="px-4 py-3 border-t border-gray-100">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-6">
                                    <!-- Facebook-style Reactions for Original Post -->
                                    <x-facebook-reactions :target="$share->post" target-type="post" :show-count="false" />

                                    <!-- Comment Button -->
                                    <button onclick="openCommentModal({{ $share->post->id }})" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                        <span class="text-sm">Comment</span>
                                    </button>

                                    <!-- Share Button -->
                                    <button onclick="openShareModal({{ $share->post->id }})" class="flex items-center space-x-2 text-gray-500 hover:text-green-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                        <span class="text-sm">Share</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Share Comments -->
            <div class="w-1/2 flex flex-col">
                <!-- Share Interaction Stats -->
                <div class="p-4 border-b border-gray-100 bg-gray-50">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div class="flex items-center space-x-4">
                            @if($share->reactions && $share->reactions->count() > 0)
                                <span class="flex items-center space-x-1">
                                    @php
                                        $shareReactionCounts = $share->reactions->groupBy('type')->map->count()->toArray();
                                        arsort($shareReactionCounts);
                                        $topShareReactions = array_slice($shareReactionCounts, 0, 3, true);
                                    @endphp
                                    @foreach($topShareReactions as $type => $count)
                                        @php $details = \App\Models\Reaction::getReactionDetails($type); @endphp
                                        <img src="{{ $details['emoji'] }}" alt="{{ $details['label'] }}" class="w-4 h-4">
                                    @endforeach
                                    <span>{{ $share->reactions->count() }}</span>
                                </span>
                            @endif
                            
                            @if($share->comments->count() > 0)
                                <span id="modal-share-comments-count-{{ $share->id }}">{{ $share->comments->count() }} comments</span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Share Action Buttons -->
                <div class="px-4 py-3 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-6">
                            <!-- Facebook-style Reactions for Share -->
                            <x-facebook-reactions :target="$share" target-type="share" :show-count="false" />

                            <!-- Comment Button -->
                            <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                                <span class="text-sm">Comment</span>
                            </button>

                            <!-- Share Original Post Button -->
                            <button onclick="openShareModal({{ $share->post->id }})" class="flex items-center space-x-2 text-gray-500 hover:text-green-600 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                </svg>
                                <span class="text-sm">Share</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="flex-1 flex flex-col overflow-hidden">
                    <!-- Add Comment Form -->
                    @auth
                        <div class="p-4 border-b border-gray-50">
                            <form class="share-comment-form" data-share-id="{{ $share->id }}" data-parent-id="">
                                @csrf
                                <div class="flex space-x-3">
                                    <div class="flex-shrink-0">
                                        <img class="h-10 w-10 rounded-full ring-2 ring-white shadow-sm"
                                             src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                                             alt="{{ auth()->user()->name }}">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="relative">
                                            <textarea name="content" rows="1"
                                                      placeholder="Write a comment..."
                                                      class="w-full px-4 py-3 border border-gray-200 rounded-full shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm bg-gray-50 hover:bg-white transition-colors duration-200 placeholder-gray-400"
                                                      required></textarea>
                                            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 transition-opacity duration-200" id="modal-comment-submit-btn-{{ $share->id }}">
                                                <button type="submit"
                                                        class="p-2 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200 hover:scale-105">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    @endauth

                    <!-- Comments List -->
                    <div class="flex-1 overflow-y-auto">
                        <div class="comments-list divide-y divide-gray-100" id="modal-share-comments-list-{{ $share->id }}">
                            @forelse($share->comments->whereNull('parent_id') as $comment)
                                <x-modal-share-comment-item :comment="$comment" :share="$share" />
                            @empty
                                <div class="no-comments text-gray-500 text-center py-12 px-4">
                                    <div class="max-w-sm mx-auto">
                                        <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-lg font-medium text-gray-900 mb-2">No comments yet</h3>
                                        <p class="text-gray-500">Be the first to share your thoughts!</p>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Share Comment Modal Enhancements */
.share-comment-form textarea:focus {
    min-height: 100px;
    border-radius: 1rem;
    transition: all 0.3s ease;
    background-color: white;
}

.share-comment-form textarea:focus + div #modal-comment-submit-btn-{{ $share->id }} {
    opacity: 1;
}

/* Modal specific styling */
#shareCommentModal-{{ $share->id }} .comment-item {
    transition: all 0.2s ease;
}

#shareCommentModal-{{ $share->id }} .comment-item:hover {
    background-color: rgba(249, 250, 251, 0.5);
}

/* Scrollbar styling for modal */
#shareCommentModal-{{ $share->id }} .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

#shareCommentModal-{{ $share->id }} .overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
}

#shareCommentModal-{{ $share->id }} .overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

#shareCommentModal-{{ $share->id }} .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced comment form interactions for share modal
    const shareCommentForm = document.querySelector('#shareCommentModal-{{ $share->id }} .share-comment-form');

    if (shareCommentForm) {
        const textarea = shareCommentForm.querySelector('textarea[name="content"]');
        const submitBtn = shareCommentForm.querySelector('button[type="submit"]');

        if (textarea && submitBtn) {
            // Show/hide submit button based on content
            textarea.addEventListener('input', function() {
                const submitBtnContainer = shareCommentForm.querySelector('[id^="modal-comment-submit-btn-"]');
                if (this.value.trim().length > 0) {
                    if (submitBtnContainer) {
                        submitBtnContainer.style.opacity = '1';
                    }
                } else {
                    if (submitBtnContainer) {
                        submitBtnContainer.style.opacity = '0';
                    }
                }
            });

            // Auto-resize textarea
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Focus effects
            textarea.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            textarea.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.parentElement.classList.remove('focused');
                }
            });
        }
    }
});
</script>
